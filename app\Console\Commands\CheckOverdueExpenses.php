<?php

namespace App\Console\Commands;

use App\Models\Expense;
use App\Models\User;
use Filament\Notifications\Notification;
use Illuminate\Console\Command;
use Carbon\Carbon;

class CheckOverdueExpenses extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'expenses:check-overdue';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Verifica despesas em atraso e envia notificações';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Verificando despesas em atraso...');

        // Buscar despesas não pagas com mais de 30 dias
        $overdueExpenses = Expense::where('paid', false)
            ->where('expense_date', '<=', Carbon::now()->subDays(30))
            ->with(['expenseCategory', 'user'])
            ->get();

        if ($overdueExpenses->isEmpty()) {
            $this->info('Nenhuma despesa em atraso encontrada.');
            return;
        }

        $this->info("Encontradas {$overdueExpenses->count()} despesas em atraso.");

        // Agrupar por usuário responsável
        $expensesByUser = $overdueExpenses->groupBy('user_id');

        foreach ($expensesByUser as $userId => $userExpenses) {
            $user = User::find($userId);
            if (!$user) continue;

            $totalAmount = $userExpenses->sum('amount');
            $count = $userExpenses->count();

            // Criar notificação no Filament
            Notification::make()
                ->title('Despesas em Atraso')
                ->body("Você tem {$count} despesas não pagas totalizando R$ " . number_format($totalAmount, 2, ',', '.'))
                ->warning()
                ->persistent()
                ->sendToDatabase($user);

            $this->info("Notificação enviada para {$user->name} - {$count} despesas em atraso.");
        }

        // Notificar administradores sobre o total geral
        $adminUsers = User::whereHas('roles', function ($query) {
            $query->where('name', 'admin');
        })->get();

        if ($adminUsers->isNotEmpty()) {
            $totalOverdueAmount = $overdueExpenses->sum('amount');
            $totalCount = $overdueExpenses->count();

            foreach ($adminUsers as $admin) {
                Notification::make()
                    ->title('Relatório de Despesas em Atraso')
                    ->body("Total de {$totalCount} despesas em atraso no sistema, totalizando R$ " . number_format($totalOverdueAmount, 2, ',', '.'))
                    ->danger()
                    ->persistent()
                    ->sendToDatabase($admin);
            }

            $this->info("Notificação de resumo enviada para {$adminUsers->count()} administradores.");
        }

        $this->info('Verificação concluída!');
    }
}
