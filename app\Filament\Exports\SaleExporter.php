<?php

namespace App\Filament\Exports;

use App\Models\Sale;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;

class SaleExporter extends Exporter
{
    protected static ?string $model = Sale::class;

    public static function getColumns(): array
    {
        return [
            ExportColumn::make('id')
                ->label('ID'),

            ExportColumn::make('sale_date')
                ->label('Data da Venda')
                ->formatStateUsing(fn ($state) => $state ? \Carbon\Carbon::parse($state)->format('d/m/Y') : ''),

            ExportColumn::make('person.name')
                ->label('Cliente'),

            ExportColumn::make('person.department.name')
                ->label('Departamento'),

            ExportColumn::make('product.name')
                ->label('Produto'),

            ExportColumn::make('quantity')
                ->label('Quantidade'),

            ExportColumn::make('unit_price')
                ->label('Preço Unitário')
                ->formatStateUsing(fn ($state) => 'R$ ' . number_format($state, 2, ',', '.')),

            ExportColumn::make('total')
                ->label('Total')
                ->formatStateUsing(fn ($state) => 'R$ ' . number_format($state, 2, ',', '.')),

            ExportColumn::make('paid')
                ->label('Status')
                ->formatStateUsing(fn ($state) => $state ? 'Pago' : 'Pendente'),

            ExportColumn::make('payment_date')
                ->label('Data do Pagamento')
                ->formatStateUsing(fn ($state) => $state ? \Carbon\Carbon::parse($state)->format('d/m/Y') : ''),

            ExportColumn::make('observations')
                ->label('Observações'),

            ExportColumn::make('created_at')
                ->label('Criado em')
                ->formatStateUsing(fn ($state) => $state ? \Carbon\Carbon::parse($state)->format('d/m/Y H:i') : ''),
        ];
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        $body = 'Sua exportação de vendas foi concluída e ' . number_format($export->successful_rows) . ' ' . str('linha')->plural($export->successful_rows) . ' foram exportadas.';

        if ($failedRowsCount = $export->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('linha')->plural($failedRowsCount) . ' falharam na exportação.';
        }

        return $body;
    }
}
