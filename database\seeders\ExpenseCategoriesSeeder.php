<?php

namespace Database\Seeders;

use App\Models\ExpenseCategory;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ExpenseCategoriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Alimentação',
                'description' => 'Gastos com alimentação e bebidas',
                'color' => '#10B981',
                'active' => true,
            ],
            [
                'name' => 'Transporte',
                'description' => 'Gastos com combustível, manutenção de veículos e transporte',
                'color' => '#3B82F6',
                'active' => true,
            ],
            [
                'name' => 'Material de Limpeza',
                'description' => 'Produtos de limpeza e higiene',
                'color' => '#8B5CF6',
                'active' => true,
            ],
            [
                'name' => 'Equipamentos',
                'description' => 'Compra e manutenção de equipamentos',
                'color' => '#F59E0B',
                'active' => true,
            ],
            [
                'name' => 'Marketing',
                'description' => 'Gastos com publicidade e marketing',
                'color' => '#EF4444',
                'active' => true,
            ],
            [
                'name' => 'Energia Elétrica',
                'description' => 'Conta de luz',
                'color' => '#FBBF24',
                'active' => true,
            ],
            [
                'name' => 'Água',
                'description' => 'Conta de água',
                'color' => '#06B6D4',
                'active' => true,
            ],
            [
                'name' => 'Internet/Telefone',
                'description' => 'Gastos com telecomunicações',
                'color' => '#84CC16',
                'active' => true,
            ],
            [
                'name' => 'Aluguel',
                'description' => 'Aluguel do estabelecimento',
                'color' => '#6B7280',
                'active' => true,
            ],
            [
                'name' => 'Impostos e Taxas',
                'description' => 'Impostos, taxas e tributos',
                'color' => '#DC2626',
                'active' => true,
            ],
            [
                'name' => 'Manutenção',
                'description' => 'Manutenção geral do estabelecimento',
                'color' => '#7C3AED',
                'active' => true,
            ],
            [
                'name' => 'Outros',
                'description' => 'Outras despesas diversas',
                'color' => '#6B7280',
                'active' => true,
            ],
        ];

        foreach ($categories as $category) {
            ExpenseCategory::create($category);
        }
    }
}
