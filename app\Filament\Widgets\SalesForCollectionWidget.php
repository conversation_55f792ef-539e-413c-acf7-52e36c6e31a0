<?php

namespace App\Filament\Widgets;

use App\Models\Sale;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Carbon\Carbon;

class SalesForCollectionWidget extends BaseWidget
{
    protected static ?int $sort = 10;

    public static function canView(): bool
    {
        return false;
    }

    protected function getStats(): array
    {
        $now = Carbon::now();

        // Vendas do mês passado que devem ser cobradas agora
        $previousMonth = $now->copy()->startOfMonth()->subMonth();
        $endOfPreviousMonth = $previousMonth->copy()->endOfMonth();

        $salesForCollection = Sale::whereBetween('sale_date', [$previousMonth, $endOfPreviousMonth])
            ->where('paid', false)
            ->get();

        $totalValue = $salesForCollection->sum('total');
        $totalQuantity = $salesForCollection->sum('quantity');
        $uniqueClients = $salesForCollection->pluck('person_id')->unique()->count();

        // Comparação com o mês anterior (vendas de 2 meses atrás)
        $twoMonthsAgo = $now->copy()->startOfMonth()->subMonths(2);
        $endOfTwoMonthsAgo = $twoMonthsAgo->copy()->endOfMonth();

        $previousCollectionSales = Sale::whereBetween('sale_date', [$twoMonthsAgo, $endOfTwoMonthsAgo])
            ->where('paid', false)
            ->get();

        $previousTotalValue = $previousCollectionSales->sum('total');

        $valueChange = $previousTotalValue > 0
            ? (($totalValue - $previousTotalValue) / $previousTotalValue) * 100
            : 0;

        $monthName = $previousMonth->locale('pt_BR')->isoFormat('MMMM YYYY');

        return [
            Stat::make('💰 Valor para Cobrança', 'R$ ' . number_format($totalValue, 2, ',', '.'))
                ->description('Vendas de ' . $monthName . ' para cobrar agora')
                ->descriptionIcon('heroicon-m-currency-dollar')
                ->color('warning')
                ->chart([5, 8, 12, 15, 18, 22, 25]),

            Stat::make('👥 Clientes para Cobrar', $uniqueClients . ' clientes')
                ->description('Valor médio: R$ ' . number_format($uniqueClients > 0 ? $totalValue / $uniqueClients : 0, 2, ',', '.'))
                ->descriptionIcon('heroicon-m-users')
                ->color('info')
                ->chart([3, 5, 7, 9, 11, 13, 15]),

            Stat::make('📦 Itens para Cobrança', number_format($totalQuantity, 0, ',', '.') . ' itens')
                ->description(
                    $valueChange >= 0
                        ? '+' . number_format($valueChange, 1) . '% vs mês anterior'
                        : number_format($valueChange, 1) . '% vs mês anterior'
                )
                ->descriptionIcon($valueChange >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($valueChange >= 0 ? 'success' : 'danger')
                ->chart([2, 4, 6, 8, 10, 12, 14]),
        ];
    }
}
