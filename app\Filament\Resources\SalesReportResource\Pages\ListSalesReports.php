<?php

namespace App\Filament\Resources\SalesReportResource\Pages;

use App\Filament\Exports\SaleExporter;
use App\Filament\Resources\SalesReportResource;
use App\Models\Sale;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;

class ListSalesReports extends ListRecords
{
    protected static string $resource = SalesReportResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ExportAction::make()
                ->exporter(SaleExporter::class)
                ->label('Exportar Relatório')
                ->color('success')
                ->icon('heroicon-o-arrow-down-tray'),
        ];
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make('Todas as Vendas')
                ->badge(Sale::count()),

            'paid' => Tab::make('Vendas Pagas')
                ->modifyQueryUsing(fn(Builder $query) => $query->where('paid', true))
                ->badge(Sale::where('paid', true)->count())
                ->badgeColor('success'),

            'pending' => Tab::make('Vendas Pendentes')
                ->modifyQueryUsing(fn(Builder $query) => $query->where('paid', false))
                ->badge(Sale::where('paid', false)->count())
                ->badgeColor('danger'),

            'this_month' => Tab::make('Este Mês')
                ->modifyQueryUsing(fn(Builder $query) => $query->whereMonth('sale_date', now()->month)
                    ->whereYear('sale_date', now()->year))
                ->badge(Sale::whereMonth('sale_date', now()->month)
                    ->whereYear('sale_date', now()->year)->count())
                ->badgeColor('primary'),

            'last_month' => Tab::make('Mês Passado')
                ->modifyQueryUsing(function (Builder $query) {
                    $lastMonth = now()->startOfMonth()->subMonth();
                    return $query->whereMonth('sale_date', $lastMonth->month)
                        ->whereYear('sale_date', $lastMonth->year);
                })
                ->badge(function () {
                    $lastMonth = now()->startOfMonth()->subMonth();
                    return Sale::whereMonth('sale_date', $lastMonth->month)
                        ->whereYear('sale_date', $lastMonth->year)->count();
                })
                ->badgeColor('gray'),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            \App\Filament\Resources\SalesReportResource\Widgets\SalesStatsWidget::class,
        ];
    }

    protected function getFooterWidgets(): array
    {
        return [
            \App\Filament\Resources\SalesReportResource\Widgets\SalesChartWidget::class,
            \App\Filament\Resources\SalesReportResource\Widgets\ProductSalesWidget::class,
            \App\Filament\Resources\SalesReportResource\Widgets\DepartmentSalesWidget::class,
            \App\Filament\Resources\SalesReportResource\Widgets\TopCustomersWidget::class,
        ];
    }

    public function getTitle(): string
    {
        return 'Relatório de Vendas';
    }

    public function getSubheading(): ?string
    {
        $totalSales = Sale::sum('total');
        $totalQuantity = Sale::sum('quantity');
        $pendingAmount = Sale::where('paid', false)->sum('total');

        return "Total de vendas: R$ " . number_format($totalSales, 2, ',', '.') .
            " | Quantidade total: " . number_format($totalQuantity, 0, ',', '.') .
            " | Pendente: R$ " . number_format($pendingAmount, 2, ',', '.');
    }
}
