<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Expense extends Model
{
    protected $fillable = [
        'description',
        'amount',
        'expense_date',
        'expense_category_id',
        'payment_method',
        'paid',
        'payment_date',
        'notes',
        'user_id',
    ];

    protected $casts = [
        'expense_date' => 'date',
        'payment_date' => 'date',
        'paid' => 'boolean',
        'amount' => 'decimal:2',
    ];

    public function expenseCategory()
    {
        return $this->belongsTo(ExpenseCategory::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function getPaymentMethodLabelAttribute()
    {
        $methods = [
            'dinheiro' => 'Dinheiro',
            'pix' => 'PIX',
            'cartao_debito' => 'Cartão de Débito',
            'cartao_credito' => 'Cartão de Crédito',
            'transferencia' => 'Transferência',
            'boleto' => 'Boleto',
        ];

        return $methods[$this->payment_method] ?? $this->payment_method;
    }

    public function scopePaid($query)
    {
        return $query->where('paid', true);
    }

    public function scopeUnpaid($query)
    {
        return $query->where('paid', false);
    }
}
