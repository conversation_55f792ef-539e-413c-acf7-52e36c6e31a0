<?php

namespace App\Filament\Widgets;

use App\Models\Sale;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Carbon\Carbon;

class OverdueSalesWidget extends BaseWidget
{
    protected static ?int $sort = 30;

    public static function canView(): bool
    {
        return false;
    }

    protected function getStats(): array
    {
        $now = Carbon::now();

        // Vendas de 2+ meses atrás ainda não pagas (consideradas em atraso)
        $twoMonthsAgo = $now->copy()->startOfMonth()->subMonths(2);

        $overdueSales = Sale::where('sale_date', '<', $twoMonthsAgo)
            ->where('paid', false)
            ->get();

        $totalOverdueValue = $overdueSales->sum('total');
        $overdueClients = $overdueSales->pluck('person_id')->unique()->count();
        $overdueCount = $overdueSales->count();

        // Vendas críticas (3+ meses em atraso)
        $threeMonthsAgo = $now->copy()->startOfMonth()->subMonths(3);

        $criticalSales = Sale::where('sale_date', '<', $threeMonthsAgo)
            ->where('paid', false)
            ->get();

        $criticalValue = $criticalSales->sum('total');
        $criticalClients = $criticalSales->pluck('person_id')->unique()->count();

        // Vendas muito antigas (6+ meses)
        $sixMonthsAgo = $now->copy()->startOfMonth()->subMonths(6);

        $veryOldSales = Sale::where('sale_date', '<', $sixMonthsAgo)
            ->where('paid', false)
            ->get();

        $veryOldValue = $veryOldSales->sum('total');

        // Percentual crítico
        $criticalPercentage = $totalOverdueValue > 0 ? ($criticalValue / $totalOverdueValue) * 100 : 0;

        return [
            Stat::make('🚨 Vendas em Atraso', 'R$ ' . number_format($totalOverdueValue, 2, ',', '.'))
                ->description($overdueCount . ' vendas de ' . $overdueClients . ' clientes')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color('danger')
                ->chart([10, 15, 20, 25, 30, 35, 40]),

            Stat::make('⚠️ Situação Crítica', 'R$ ' . number_format($criticalValue, 2, ',', '.'))
                ->description($criticalClients . ' clientes com 3+ meses atraso')
                ->descriptionIcon('heroicon-m-fire')
                ->color('danger')
                ->chart([5, 8, 12, 15, 18, 22, 25]),

            Stat::make('💀 Perdas Prováveis', 'R$ ' . number_format($veryOldValue, 2, ',', '.'))
                ->description(number_format($criticalPercentage, 1) . '% do total em atraso é crítico')
                ->descriptionIcon('heroicon-m-x-circle')
                ->color('gray')
                ->chart([2, 3, 5, 7, 9, 12, 15]),
        ];
    }
}
