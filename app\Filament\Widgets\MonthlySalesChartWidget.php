<?php

namespace App\Filament\Widgets;

use App\Models\Sale;
use Filament\Widgets\ChartWidget;
use Carbon\Carbon;

class MonthlySalesChartWidget extends ChartWidget
{
    protected static ?string $heading = 'Vendas por Dia - Valor e Quantidade (Últimos 30 Dias)';

    protected static ?int $sort = 4;

    protected int | string | array $columnSpan = 'half';

    protected function getData(): array
    {
        $salesValueData = [];
        $salesQuantityData = [];
        $labels = [];

        // Últimos 30 dias
        for ($i = 29; $i >= 0; $i--) {
            $day = Carbon::now()->subDays($i);

            // Valor das vendas do dia
            $salesValue = Sale::whereDate('sale_date', $day->format('Y-m-d'))
                ->sum('total');

            // Quantidade de vendas do dia
            $salesQuantity = Sale::whereDate('sale_date', $day->format('Y-m-d'))
                ->sum('quantity');

            $salesValueData[] = (float) $salesValue;
            $salesQuantityData[] = (float) $salesQuantity;
            $labels[] = $this->getDayLabel($day);
        }

        return [
            'datasets' => [
                [
                    'label' => 'Valor das Vendas (R$)',
                    'data' => $salesValueData,
                    'borderColor' => 'rgb(59, 130, 246)',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'fill' => true,
                    'tension' => 0.4,
                    'yAxisID' => 'y',
                ],
                [
                    'label' => 'Quantidade Vendida',
                    'data' => $salesQuantityData,
                    'borderColor' => 'rgb(34, 197, 94)',
                    'backgroundColor' => 'rgba(34, 197, 94, 0.1)',
                    'fill' => false,
                    'tension' => 0.4,
                    'yAxisID' => 'y1',
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'tooltip' => [
                    'mode' => 'index',
                    'intersect' => false,

                ]
            ],
            'scales' => [
                'y' => [
                    'type' => 'linear',
                    'display' => true,
                    'position' => 'left',
                    'beginAtZero' => true,
                    'title' => [
                        'display' => true,
                        'text' => 'Valor (R$)'
                    ],
                    'ticks' => [
                        'callback' => 'function(value) {
                            return "R$ " + value.toLocaleString("pt-BR", {minimumFractionDigits: 2});
                        }'
                    ]
                ],
                'y1' => [
                    'type' => 'linear',
                    'display' => true,
                    'position' => 'right',
                    'beginAtZero' => true,
                    'title' => [
                        'display' => true,
                        'text' => 'Quantidade'
                    ],
                    'grid' => [
                        'drawOnChartArea' => false,
                    ],
                    'ticks' => [
                        'callback' => 'function(value) {
                            return value.toLocaleString("pt-BR") + " un";
                        }'
                    ]
                ]
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
            'interaction' => [
                'mode' => 'index',
                'intersect' => false,
            ],
        ];
    }

    private function getDayLabel(Carbon $date): string
    {
        // Para os últimos 30 dias, mostrar apenas dia/mês
        // Se for hoje, mostrar "Hoje"
        // Se for ontem, mostrar "Ontem"
        $today = Carbon::now();

        if ($date->isSameDay($today)) {
            return 'Hoje';
        } elseif ($date->isSameDay($today->copy()->subDay())) {
            return 'Ontem';
        } else {
            return $date->format('d/m');
        }
    }
}
