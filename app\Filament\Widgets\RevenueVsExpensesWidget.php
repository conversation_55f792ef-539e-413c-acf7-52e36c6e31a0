<?php

namespace App\Filament\Widgets;

use App\Models\Sale;
use App\Models\Expense;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Carbon\Carbon;

class RevenueVsExpensesWidget extends BaseWidget
{
    protected static ?int $sort = 52;

    public static function canView(): bool
    {
        return false;
    }

    protected function getStats(): array
    {
        $now = Carbon::now();

        // Período atual (este mês)
        $currentMonth = $now->copy()->startOfMonth();
        $endOfMonth = $now->copy()->endOfMonth();

        // Receitas (vendas pagas este mês)
        $currentRevenue = Sale::where('paid', true)
            ->whereBetween('payment_date', [$currentMonth, $endOfMonth])
            ->sum('total');

        // Despesas deste mês
        $currentExpenses = Expense::whereBetween('expense_date', [$currentMonth, $endOfMonth])
            ->sum('amount');

        // Lucro/Prejuízo
        $profit = $currentRevenue - $currentExpenses;
        $profitMargin = $currentRevenue > 0 ? ($profit / $currentRevenue) * 100 : 0;

        // Comparação com mês anterior
        $previousMonth = $now->copy()->startOfMonth()->subMonth();
        $endOfPreviousMonth = $previousMonth->copy()->endOfMonth();

        $previousRevenue = Sale::where('paid', true)
            ->whereBetween('payment_date', [$previousMonth, $endOfPreviousMonth])
            ->sum('total');

        $previousExpenses = Expense::whereBetween('expense_date', [$previousMonth, $endOfPreviousMonth])
            ->sum('amount');

        $previousProfit = $previousRevenue - $previousExpenses;

        // Variação do lucro
        $profitVariation = 0;
        if ($previousProfit != 0) {
            $profitVariation = (($profit - $previousProfit) / abs($previousProfit)) * 100;
        }

        return [
            Stat::make('💰 Receitas Este Mês', 'R$ ' . number_format($currentRevenue, 2, ',', '.'))
                ->description('Vendas pagas neste período')
                ->descriptionIcon('heroicon-m-banknotes')
                ->color('success')
                ->chart([20, 25, 30, 35, 40, 45, 50]),

            Stat::make('💸 Despesas Este Mês', 'R$ ' . number_format($currentExpenses, 2, ',', '.'))
                ->description('Total de gastos do período')
                ->descriptionIcon('heroicon-m-credit-card')
                ->color('danger')
                ->chart([10, 15, 18, 22, 25, 28, 30]),

            Stat::make(
                $profit >= 0 ? '📈 Lucro Este Mês' : '📉 Prejuízo Este Mês',
                'R$ ' . number_format(abs($profit), 2, ',', '.')
            )
                ->description(
                    'Margem: ' . number_format($profitMargin, 1) . '% | ' .
                        ($profitVariation >= 0 ? '+' : '') . number_format($profitVariation, 1) . '% vs anterior'
                )
                ->descriptionIcon($profit >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($profit >= 0 ? 'success' : 'danger')
                ->chart($profit >= 0 ? [5, 10, 15, 20, 25, 30, 35] : [35, 30, 25, 20, 15, 10, 5]),
        ];
    }
}
