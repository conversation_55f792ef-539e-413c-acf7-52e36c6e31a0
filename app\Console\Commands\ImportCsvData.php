<?php

namespace App\Console\Commands;

use App\Models\Department;
use App\Models\Person;
use App\Models\Product;
use App\Models\Sale;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ImportCsvData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:csv-data {file=import.csv}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Importa dados do arquivo CSV para criar pessoas, setores e vendas';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $fileName = $this->argument('file');
        $filePath = base_path($fileName);

        if (!file_exists($filePath)) {
            $this->error("Arquivo {$fileName} não encontrado!");
            return 1;
        }

        $this->info("Iniciando importação do arquivo: {$fileName}");

        // Criar produto padrão se não existir
        $defaultProduct = $this->getOrCreateDefaultProduct();
        
        // Criar setor "Desconhecido" se não existir
        $unknownDepartment = $this->getOrCreateUnknownDepartment();

        $csvData = $this->readCsvFile($filePath);
        
        if (empty($csvData)) {
            $this->error('Nenhum dado válido encontrado no arquivo CSV!');
            return 1;
        }

        $this->info("Encontrados " . count($csvData) . " registros para processar.");

        $processed = 0;
        $errors = 0;

        DB::beginTransaction();

        try {
            foreach ($csvData as $index => $row) {
                try {
                    $this->processRow($row, $defaultProduct, $unknownDepartment);
                    $processed++;
                    
                    if ($processed % 10 == 0) {
                        $this->info("Processados {$processed} registros...");
                    }
                } catch (\Exception $e) {
                    $errors++;
                    $this->warn("Erro na linha " . ($index + 2) . ": " . $e->getMessage());
                }
            }

            DB::commit();
            
            $this->info("Importação concluída!");
            $this->info("Registros processados: {$processed}");
            if ($errors > 0) {
                $this->warn("Registros com erro: {$errors}");
            }

        } catch (\Exception $e) {
            DB::rollBack();
            $this->error("Erro durante a importação: " . $e->getMessage());
            return 1;
        }

        return 0;
    }

    /**
     * Lê o arquivo CSV e retorna os dados
     */
    private function readCsvFile($filePath)
    {
        $csvData = [];
        
        if (($handle = fopen($filePath, "r")) !== FALSE) {
            $header = fgetcsv($handle, 1000, ";"); // Lê o cabeçalho
            
            while (($data = fgetcsv($handle, 1000, ";")) !== FALSE) {
                // Pula linhas vazias
                if (empty(array_filter($data))) {
                    continue;
                }
                
                // Pula se não tem nome
                if (empty(trim($data[0]))) {
                    continue;
                }
                
                $csvData[] = [
                    'nome' => trim($data[0]),
                    'setor' => trim($data[1] ?? ''),
                    'quantidade' => (int) ($data[2] ?? 1),
                    'telefone' => trim($data[3] ?? ''),
                    'data_compra' => trim($data[4] ?? ''),
                    'observacao' => trim($data[5] ?? ''),
                ];
            }
            fclose($handle);
        }
        
        return $csvData;
    }

    /**
     * Obtém ou cria o produto padrão
     */
    private function getOrCreateDefaultProduct()
    {
        $product = Product::first();
        
        if (!$product) {
            $product = Product::create([
                'name' => 'Produto Padrão',
                'price' => 10.00, // Preço padrão de R$ 10,00
            ]);
            $this->info("Produto padrão criado: {$product->name}");
        } else {
            $this->info("Usando produto existente: {$product->name}");
        }
        
        return $product;
    }

    /**
     * Obtém ou cria o setor "Desconhecido"
     */
    private function getOrCreateUnknownDepartment()
    {
        $department = Department::firstOrCreate(
            ['name' => 'Desconhecido']
        );
        
        if ($department->wasRecentlyCreated) {
            $this->info("Setor 'Desconhecido' criado.");
        }
        
        return $department;
    }

    /**
     * Processa uma linha do CSV
     */
    private function processRow($row, $defaultProduct, $unknownDepartment)
    {
        // 1. Criar ou encontrar o setor
        $department = $unknownDepartment;
        if (!empty($row['setor'])) {
            $department = Department::firstOrCreate(
                ['name' => $row['setor']]
            );
        }

        // 2. Criar ou encontrar a pessoa
        $person = Person::firstOrCreate(
            ['name' => $row['nome']],
            [
                'phone' => $row['telefone'],
                'department_id' => $department->id,
            ]
        );

        // Se a pessoa já existia, atualizar o telefone se fornecido e não existia antes
        if (!$person->wasRecentlyCreated && !empty($row['telefone']) && empty($person->phone)) {
            $person->update(['phone' => $row['telefone']]);
        }

        // 3. Converter data
        $saleDate = $this->parseDate($row['data_compra']);

        // 4. Verificar se está pago
        $isPaid = stripos($row['observacao'], 'pago') !== false;

        // 5. Criar a venda
        $sale = Sale::create([
            'person_id' => $person->id,
            'product_id' => $defaultProduct->id,
            'quantity' => $row['quantidade'],
            'unit_price' => $defaultProduct->price,
            'total' => $row['quantidade'] * $defaultProduct->price,
            'paid' => $isPaid,
            'sale_date' => $saleDate,
            'payment_date' => $isPaid ? $saleDate : null,
            'observations' => $row['observacao'],
        ]);

        return $sale;
    }

    /**
     * Converte string de data para Carbon
     */
    private function parseDate($dateString)
    {
        if (empty($dateString)) {
            return now();
        }

        try {
            // Formato esperado: DD/MM/YYYY
            return Carbon::createFromFormat('d/m/Y', $dateString);
        } catch (\Exception $e) {
            $this->warn("Data inválida: {$dateString}. Usando data atual.");
            return now();
        }
    }
}
