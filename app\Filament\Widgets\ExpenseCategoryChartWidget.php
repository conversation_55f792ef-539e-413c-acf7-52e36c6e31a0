<?php

namespace App\Filament\Widgets;

use App\Models\Expense;
use App\Models\ExpenseCategory;
use Filament\Widgets\ChartWidget;
use Carbon\Carbon;

class ExpenseCategoryChartWidget extends ChartWidget
{
    protected static ?string $heading = 'Despesas por Categoria (Este Mês)';

    protected static ?int $sort = 51;

    public static function canView(): bool
    {
        return false;
    }

    protected function getData(): array
    {
        $now = Carbon::now();
        $currentMonth = $now->copy()->startOfMonth();
        $endOfMonth = $now->copy()->endOfMonth();

        // Buscar despesas do mês atual agrupadas por categoria
        $expensesByCategory = Expense::with('expenseCategory')
            ->whereBetween('expense_date', [$currentMonth, $endOfMonth])
            ->get()
            ->groupBy('expense_category_id')
            ->map(function ($expenses) {
                return [
                    'category' => $expenses->first()->expenseCategory->name ?? 'Sem Categoria',
                    'color' => $expenses->first()->expenseCategory->color ?? '#6B7280',
                    'total' => $expenses->sum('amount')
                ];
            })
            ->sortByDesc('total')
            ->take(8); // Mostrar apenas as 8 principais categorias

        $labels = $expensesByCategory->pluck('category')->toArray();
        $data = $expensesByCategory->pluck('total')->toArray();
        $colors = $expensesByCategory->pluck('color')->toArray();

        return [
            'datasets' => [
                [
                    'label' => 'Despesas por Categoria',
                    'data' => $data,
                    'backgroundColor' => $colors,
                    'borderColor' => $colors,
                    'borderWidth' => 2,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'pie';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
                'tooltip' => [
                    'callbacks' => [
                        'label' => 'function(context) {
                            return context.label + ": R$ " + context.parsed.toLocaleString("pt-BR", {minimumFractionDigits: 2});
                        }'
                    ]
                ]
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
        ];
    }
}
