<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ExpenseReportResource\Pages;
use App\Models\Expense;
use App\Models\ExpenseCategory;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class ExpenseReportResource extends Resource
{
    protected static ?string $model = Expense::class;

    protected static ?string $label = 'Relatório de Despesas';
    protected static ?string $pluralModelLabel = 'Relatórios de Despesas';

    protected static ?string $navigationIcon = 'phosphor-chart-line';
    protected static ?string $navigationGroup = 'Relatórios';
    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // Não precisamos de formulário para relatórios
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('expense_date')
                    ->label('Data')
                    ->date('d/m/Y')
                    ->sortable(),

                Tables\Columns\TextColumn::make('description')
                    ->label('Descrição')
                    ->searchable()
                    ->limit(40),

                Tables\Columns\TextColumn::make('expenseCategory.name')
                    ->label('Categoria')
                    ->badge()
                    ->color(fn (Expense $record): string => $record->expenseCategory->color ?? 'gray')
                    ->sortable(),

                Tables\Columns\TextColumn::make('amount')
                    ->label('Valor')
                    ->money('BRL')
                    ->sortable()
                    ->summarize([
                        Tables\Columns\Summarizers\Sum::make()
                            ->money('BRL')
                            ->label('Total'),
                    ]),

                Tables\Columns\TextColumn::make('payment_method_label')
                    ->label('Método')
                    ->badge()
                    ->color('info'),

                Tables\Columns\IconColumn::make('paid')
                    ->label('Status')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

                Tables\Columns\TextColumn::make('user.name')
                    ->label('Usuário')
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('expense_category_id')
                    ->label('Categoria')
                    ->relationship('expenseCategory', 'name')
                    ->searchable()
                    ->preload(),

                Tables\Filters\SelectFilter::make('payment_method')
                    ->label('Método de Pagamento')
                    ->options([
                        'dinheiro' => 'Dinheiro',
                        'pix' => 'PIX',
                        'cartao_debito' => 'Cartão de Débito',
                        'cartao_credito' => 'Cartão de Crédito',
                        'transferencia' => 'Transferência',
                        'boleto' => 'Boleto',
                    ])
                    ->native(false),

                Tables\Filters\TernaryFilter::make('paid')
                    ->label('Status de Pagamento')
                    ->boolean()
                    ->trueLabel('Apenas pagas')
                    ->falseLabel('Apenas não pagas')
                    ->native(false),

                Tables\Filters\Filter::make('expense_date')
                    ->form([
                        Forms\Components\DatePicker::make('from')
                            ->label('De')
                            ->default(now()->startOfMonth()),
                        Forms\Components\DatePicker::make('until')
                            ->label('Até')
                            ->default(now()->endOfMonth()),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('expense_date', '>=', $date),
                            )
                            ->when(
                                $data['until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('expense_date', '<=', $date),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['from'] ?? null) {
                            $indicators['from'] = 'De: ' . Carbon::parse($data['from'])->format('d/m/Y');
                        }
                        if ($data['until'] ?? null) {
                            $indicators['until'] = 'Até: ' . Carbon::parse($data['until'])->format('d/m/Y');
                        }
                        return $indicators;
                    }),

                Tables\Filters\Filter::make('this_month')
                    ->label('Este Mês')
                    ->query(fn (Builder $query): Builder => $query->whereBetween('expense_date', [
                        now()->startOfMonth(),
                        now()->endOfMonth()
                    ]))
                    ->toggle(),

                Tables\Filters\Filter::make('last_month')
                    ->label('Mês Passado')
                    ->query(fn (Builder $query): Builder => $query->whereBetween('expense_date', [
                        now()->subMonth()->startOfMonth(),
                        now()->subMonth()->endOfMonth()
                    ]))
                    ->toggle(),
            ])
            ->actions([
                // Sem ações de edição para relatórios
            ])
            ->bulkActions([
                // Sem ações em massa para relatórios
            ])
            ->defaultSort('expense_date', 'desc')
            ->striped()
            ->paginated([10, 25, 50, 100]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListExpenseReports::route('/'),
            // Sem páginas de criação/edição para relatórios
        ];
    }
}
