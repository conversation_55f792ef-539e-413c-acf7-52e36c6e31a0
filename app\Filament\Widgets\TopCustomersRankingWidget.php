<?php

namespace App\Filament\Widgets;

use App\Models\Person;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

class TopCustomersRankingWidget extends BaseWidget
{
    protected static ?string $heading = '🏆 Ranking de Clientes - Mês Atual';

    protected static ?int $sort = 6;

    protected int | string | array $columnSpan = 'full';

    public function table(Table $table): Table
    {
        $currentMonth = Carbon::now();

        return $table
            ->query(
                Person::query()
                    ->whereHas('sales', function (Builder $query) use ($currentMonth) {
                        $query->whereMonth('sale_date', $currentMonth->month)
                            ->whereYear('sale_date', $currentMonth->year);
                    })
                    ->withSum(['sales as total_value' => function (Builder $query) use ($currentMonth) {
                        $query->whereMonth('sale_date', $currentMonth->month)
                            ->whereYear('sale_date', $currentMonth->year);
                    }], 'total')
                    ->withSum(['sales as total_quantity' => function (Builder $query) use ($currentMonth) {
                        $query->whereMonth('sale_date', $currentMonth->month)
                            ->whereYear('sale_date', $currentMonth->year);
                    }], 'quantity')
                    ->withCount(['sales as total_orders' => function (Builder $query) use ($currentMonth) {
                        $query->whereMonth('sale_date', $currentMonth->month)
                            ->whereYear('sale_date', $currentMonth->year);
                    }])
                    ->orderBy('total_value', 'desc')
                    ->limit(10)
            )
            ->columns([
                Tables\Columns\TextColumn::make('rank')
                    ->label('#')
                    ->alignCenter()
                    ->getStateUsing(function ($record, $rowLoop) {
                        $position = $rowLoop->iteration;
                        return match($position) {
                            1 => '🥇',
                            2 => '🥈',
                            3 => '🥉',
                            default => $position . 'º'
                        };
                    })
                    ->weight('bold')
                    ->size('lg'),

                Tables\Columns\TextColumn::make('name')
                    ->label('Cliente')
                    ->searchable()
                    ->weight('bold')
                    ->icon('heroicon-m-user')
                    ->color(fn ($rowLoop) => match($rowLoop->iteration) {
                        1 => 'warning',  // Ouro
                        2 => 'gray',     // Prata
                        3 => 'warning',  // Bronze
                        default => 'primary'
                    }),

                Tables\Columns\TextColumn::make('department.name')
                    ->label('Departamento')
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('total_orders')
                    ->label('Pedidos')
                    ->alignCenter()
                    ->badge()
                    ->color('success')
                    ->suffix(' pedidos'),

                Tables\Columns\TextColumn::make('total_quantity')
                    ->label('Quantidade')
                    ->alignCenter()
                    ->numeric()
                    ->suffix(' un')
                    ->color('info'),

                Tables\Columns\TextColumn::make('total_value')
                    ->label('Valor Total')
                    ->money('BRL')
                    ->weight('bold')
                    ->alignEnd()
                    ->color(fn ($state) => $state > 500 ? 'success' : ($state > 200 ? 'warning' : 'gray')),

                Tables\Columns\TextColumn::make('average_order')
                    ->label('Ticket Médio')
                    ->getStateUsing(function ($record) {
                        return $record->total_orders > 0 ? $record->total_value / $record->total_orders : 0;
                    })
                    ->money('BRL')
                    ->color('primary'),

                Tables\Columns\TextColumn::make('phone')
                    ->label('Contato')
                    ->formatStateUsing(fn (string $state): string =>
                        '(' . substr($state, 0, 2) . ') ' . substr($state, 2, 5) . '-' . substr($state, 7)
                    )
                    ->copyable()
                    ->copyMessage('Telefone copiado!')
                    ->icon('heroicon-m-phone')
                    ->color('gray'),
            ])
            ->actions([
                Tables\Actions\Action::make('whatsapp')
                    ->label('WhatsApp')
                    ->icon('heroicon-o-chat-bubble-left-right')
                    ->color('success')
                    ->size('sm')
                    ->url(fn (Person $record): string =>
                        'https://api.whatsapp.com/send/?phone=' . $record->phone_with_ddi . '&text=' .
                        urlencode("Olá {$record->name}! Obrigado por ser um dos nossos melhores clientes este mês! 🏆")
                    )
                    ->openUrlInNewTab(),

                Tables\Actions\Action::make('view_sales')
                    ->label('Ver Vendas')
                    ->icon('heroicon-o-eye')
                    ->color('primary')
                    ->size('sm')
                    ->url(fn (Person $record): string =>
                        '/admin/sales?tableFilters[person_id][value]=' . $record->id
                    )
                    ->openUrlInNewTab(),
            ])
            ->emptyStateHeading('📊 Nenhuma venda este mês')
            ->emptyStateDescription('Ainda não há vendas registradas para o mês atual.')
            ->emptyStateIcon('heroicon-o-chart-bar')
            ->defaultPaginationPageOption(10)
            ->paginated(false); // Mostrar todos os 10 sem paginação
    }
}
