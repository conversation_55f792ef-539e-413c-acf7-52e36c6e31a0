<?php

namespace App\Filament\Widgets;

use App\Models\Sale;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Carbon\Carbon;

class CollectionPerformanceWidget extends BaseWidget
{
    protected static ?int $sort = 50;

    public static function canView(): bool
    {
        return false;
    }

    protected function getStats(): array
    {
        $now = Carbon::now();

        // Análise dos últimos 3 meses para ter uma visão mais ampla
        $threeMonthsAgo = $now->copy()->startOfMonth()->subMonths(3);

        $allSalesLastThreeMonths = Sale::where('sale_date', '>=', $threeMonthsAgo)->get();
        $paidSalesLastThreeMonths = Sale::where('sale_date', '>=', $threeMonthsAgo)
            ->where('paid', true)
            ->get();

        $totalSalesValue = $allSalesLastThreeMonths->sum('total');
        $totalPaidValue = $paidSalesLastThreeMonths->sum('total');

        $overallSuccessRate = $totalSalesValue > 0 ? ($totalPaidValue / $totalSalesValue) * 100 : 0;

        // Tempo médio entre venda e pagamento
        $paidSalesWithDates = Sale::where('sale_date', '>=', $threeMonthsAgo)
            ->where('paid', true)
            ->whereNotNull('payment_date')
            ->get();

        $averageDaysToPayment = 0;
        if ($paidSalesWithDates->count() > 0) {
            $totalDays = $paidSalesWithDates->sum(function ($sale) {
                return $sale->sale_date->diffInDays($sale->payment_date);
            });
            $averageDaysToPayment = $totalDays / $paidSalesWithDates->count();
        }

        // Performance do mês atual vs mês anterior
        $currentMonth = $now->copy()->startOfMonth();
        $previousMonth = $now->copy()->startOfMonth()->subMonth();
        $endOfPreviousMonth = $previousMonth->copy()->endOfMonth();

        // Vendas do mês anterior e sua taxa de pagamento atual
        $previousMonthSales = Sale::whereBetween('sale_date', [$previousMonth, $endOfPreviousMonth])->get();
        $previousMonthPaid = Sale::whereBetween('sale_date', [$previousMonth, $endOfPreviousMonth])
            ->where('paid', true)
            ->get();

        $currentPerformance = $previousMonthSales->sum('total') > 0
            ? ($previousMonthPaid->sum('total') / $previousMonthSales->sum('total')) * 100
            : 0;

        // Comparação com 2 meses atrás
        $twoMonthsAgo = $now->copy()->startOfMonth()->subMonths(2);
        $endOfTwoMonthsAgo = $twoMonthsAgo->copy()->endOfMonth();

        $twoMonthsAgoSales = Sale::whereBetween('sale_date', [$twoMonthsAgo, $endOfTwoMonthsAgo])->get();
        $twoMonthsAgoPaid = Sale::whereBetween('sale_date', [$twoMonthsAgo, $endOfTwoMonthsAgo])
            ->where('paid', true)
            ->get();

        $previousPerformance = $twoMonthsAgoSales->sum('total') > 0
            ? ($twoMonthsAgoPaid->sum('total') / $twoMonthsAgoSales->sum('total')) * 100
            : 0;

        $performanceChange = $currentPerformance - $previousPerformance;

        return [
            Stat::make('🎯 Taxa de Sucesso Geral', number_format($overallSuccessRate, 1) . '%')
                ->description('Últimos 3 meses: R$ ' . number_format($totalPaidValue, 0, ',', '.') . ' de R$ ' . number_format($totalSalesValue, 0, ',', '.'))
                ->descriptionIcon($overallSuccessRate >= 80 ? 'heroicon-m-trophy' : 'heroicon-m-chart-bar')
                ->color($overallSuccessRate >= 80 ? 'success' : ($overallSuccessRate >= 60 ? 'warning' : 'danger'))
                ->chart([60, 65, 70, 75, 80, 85, 90]),

            Stat::make('⏱️ Tempo Médio Pagamento', number_format($averageDaysToPayment, 0) . ' dias')
                ->description($averageDaysToPayment <= 30 ? 'Excelente prazo' : ($averageDaysToPayment <= 45 ? 'Prazo bom' : 'Prazo longo'))
                ->descriptionIcon('heroicon-m-clock')
                ->color($averageDaysToPayment <= 30 ? 'success' : ($averageDaysToPayment <= 45 ? 'warning' : 'danger'))
                ->chart([25, 28, 30, 32, 35, 38, 40]),

            Stat::make('📊 Performance Atual', number_format($currentPerformance, 1) . '%')
                ->description(
                    $performanceChange >= 0
                        ? '+' . number_format($performanceChange, 1) . 'pp vs período anterior'
                        : number_format($performanceChange, 1) . 'pp vs período anterior'
                )
                ->descriptionIcon($performanceChange >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($performanceChange >= 0 ? 'success' : 'danger')
                ->chart([70, 72, 75, 78, 80, 82, 85]),
        ];
    }
}
