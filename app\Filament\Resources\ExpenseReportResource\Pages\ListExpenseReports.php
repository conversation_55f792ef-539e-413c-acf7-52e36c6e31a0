<?php

namespace App\Filament\Resources\ExpenseReportResource\Pages;

use App\Filament\Exports\ExpenseExporter;
use App\Filament\Resources\ExpenseReportResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListExpenseReports extends ListRecords
{
    protected static string $resource = ExpenseReportResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ExportAction::make()
                ->exporter(ExpenseExporter::class)
                ->label('Exportar Relatório')
                ->color('success')
                ->icon('heroicon-o-arrow-down-tray'),
        ];
    }
}
