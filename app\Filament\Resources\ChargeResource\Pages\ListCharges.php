<?php

namespace App\Filament\Resources\ChargeResource\Pages;

use App\Filament\Resources\ChargeResource;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class ListCharges extends ListRecords
{
    protected static string $resource = ChargeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('configuracoes')
                ->label('Configurações')
                ->icon('heroicon-o-cog-6-tooth')
                ->color('gray')
                ->url(route('filament.admin.resources.charge-configs.edit', 1))
        ];
    }


    protected function getTableQuery(): ?Builder
    {
        return parent::getTableQuery()
            ->select(
                'person_id',
                DB::raw('ANY_VALUE(id) as id'),
                DB::raw('SUM(quantity) as quantity'),
                DB::raw('SUM(total) as total'),
                DB::raw('EXISTS (
                    SELECT 1 FROM charge_requests
                    WHERE charge_requests.person_id = sales.person_id
                ) as has_charge_requests')
            )
            ->with(['person', 'person.chargeRequests'])
            ->where('paid', false)
            ->groupBy('person_id');
    }
}
