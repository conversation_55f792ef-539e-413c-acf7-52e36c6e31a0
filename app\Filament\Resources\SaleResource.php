<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SaleResource\Pages;
use App\Filament\Resources\SaleResource\RelationManagers;
use App\Models\Department;
use App\Models\Person;
use App\Models\Product;
use App\Models\Sale;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\Indicator;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\HtmlString;
use Leandrocfe\FilamentPtbrFormFields\Money;

class SaleResource extends Resource
{
    protected static ?string $label = 'Vendas';

    protected static ?string $model = Sale::class;

    protected static ?string $navigationIcon = 'phosphor-shopping-cart';

    protected static ?int $navigationSort = 1;

    protected static ?string $navigationGroup = 'Financeiro';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('person_id')
                    ->label('Cliente')
                    ->options(Person::all()->pluck('name', 'id'))
                    ->searchable()
                    ->required()
                    ->preload()
                    ->relationship(name: 'person', titleAttribute: 'name')
                    ->createOptionForm([
                        Forms\Components\TextInput::make('name')
                            ->label('Nome')
                            ->required()
                            ->maxLength(191)
                            ->regex('/^[\pL\s\-\.]+$/u')
                            ->columnSpanFull(),

                        Forms\Components\TextInput::make('phone')
                            ->label('Telefone')
                            ->required()
                            ->maxLength(15)
                            ->mask('(99) 99999-9999'),

                        Forms\Components\Select::make('department_id')
                            ->label('Setor')
                            ->options(Department::all()->pluck('name', 'id'))
                            ->searchable()
                            ->required()
                            ->relationship(name: 'department', titleAttribute: 'name')
                            ->createOptionForm([
                                Forms\Components\TextInput::make('name')
                                    ->label('Nome')
                                    ->required()
                                    ->maxLength(191),
                            ]),
                    ]),

                Forms\Components\Select::make('product_id')
                    ->label('Produto')
                    ->options(Product::all()->pluck('name', 'id'))
                    ->searchable()
                    ->required()
                    ->reactive()
                    ->afterStateUpdated(fn($state, callable $set, callable $get) => self::updateUnitPriceAndTotal($get, $set))
                    ->preload()
                    ->relationship(name: 'product', titleAttribute: 'name')
                    ->createOptionForm([
                        Forms\Components\TextInput::make('name')
                            ->label('Nome')
                            ->required()
                            ->maxLength(191),

                        Money::make('price')
                            ->label('Preço')
                            ->required()
                    ]),

                Forms\Components\TextInput::make('quantity')
                    ->label('Quantidade')
                    ->required()
                    ->numeric()
                    ->default(1)
                    ->minValue(1)
                    ->reactive()
                    ->afterStateUpdated(fn($state, callable $set, callable $get) => self::updateUnitPriceAndTotal($get, $set)),

                Forms\Components\DateTimePicker::make('sale_date')
                    ->label('Data da Venda')
                    ->default(now())
                    ->native(false)
                    ->displayFormat('d/m/Y'),

                Forms\Components\Toggle::make('paid')
                    ->label('Pago?')
                    ->default(false)
                    ->reactive()
                    ->afterStateUpdated(function ($state, callable $set) {
                        if ($state) {
                            $set('payment_date', now());
                        } else {
                            $set('payment_date', null);
                        }
                    }),

                Forms\Components\DateTimePicker::make('payment_date')
                    ->label('Data do Pagamento')
                    ->native(false)
                    ->displayFormat('d/m/Y')
                    ->after('sale_date')
                    ->visible(fn($get) => $get('paid') === true),

                Forms\Components\Textarea::make('observations')
                    ->label('Observações')
                    ->columnSpanFull(),

                Hidden::make('unit_price')->default(0),
                Hidden::make('total')->default(0),
            ]);
    }

    private static function updateUnitPriceAndTotal(Get $get, Set $set): void
    {
        $product = Product::find($get('product_id'));
        if ($product) {
            $set('unit_price', $product->price);
            $set('total', $product->price * $get('quantity'));
        } else {
            $set('unit_price', 0);
            $set('total', 0);
        }
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('person.name')
                    ->label('Cliente')
                    ->searchable()
                    ->sortable()
                    ->formatStateUsing(function ($state, $record) {
                        return new HtmlString("
                                <div>
                                    <div><strong>{$record->person->name}</strong></div>
                                    <div class='text-sm text-gray-500'>{$record->person->department->name}</div>
                                </div>
                            ");
                    }),

                Tables\Columns\TextColumn::make('product.name')
                    ->label('Produto')
                    ->searchable()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('quantity')
                    ->label('Quantidade')
                    ->numeric()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('total')
                    ->label('Total')
                    ->sortable()
                    ->money('brl', 0, 'pt-BR')
                    ->toggleable(isToggledHiddenByDefault: false),

                Tables\Columns\TextColumn::make('sale_date')
                    ->label('Data da Venda')
                    ->dateTime('d/m/Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: false),

                Tables\Columns\IconColumn::make('paid')
                    ->label('Pago?')
                    ->boolean()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: false),

                Tables\Columns\TextColumn::make('payment_date')
                    ->label('Data do Pagamento')
                    ->dateTime('d/m/Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: false),

                Tables\Columns\TextColumn::make('observations')
                    ->label('Observações')
                    ->searchable()
                    ->limit(12)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();

                        if (strlen($state) <= $column->getCharacterLimit()) {
                            return null;
                        }

                        return $state;
                    })
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Criado em')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->dateTime('d/m/Y'),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Atualizado em')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->since()
                    ->dateTimeTooltip(),
            ])
            ->filters([
                Filter::make('is_paid')
                    ->label('Pagos')
                    ->query(fn(Builder $query): Builder => $query->where('paid', true))
                    ->toggle(),

                Filter::make('is_not_paid')
                    ->label('Não pagos')
                    ->query(fn(Builder $query): Builder => $query->where('paid', false))
                    ->toggle(),

                SelectFilter::make('person_id')
                    ->label('Cliente')
                    ->relationship('person', 'name')
                    ->searchable()
                    ->preload()
                    ->multiple(),

                Filter::make('sale_period')
                    ->form([
                        Select::make('sale_month')
                            ->label('Mês da venda')
                            ->options([
                                1 => 'Janeiro',
                                2 => 'Fevereiro',
                                3 => 'Março',
                                4 => 'Abril',
                                5 => 'Maio',
                                6 => 'Junho',
                                7 => 'Julho',
                                8 => 'Agosto',
                                9 => 'Setembro',
                                10 => 'Outubro',
                                11 => 'Novembro',
                                12 => 'Dezembro',
                            ])
                            ->placeholder('Selecione o mês da venda')
                            ->default((Carbon::now())->month)
                            ->selectablePlaceholder(true),

                        Select::make('sale_year')
                            ->label('Ano da venda')
                            ->options(self::getFilterYears())
                            ->placeholder('Selecione o ano da venda')
                            ->default((Carbon::now())->subMonth()->year)
                            ->selectablePlaceholder(true),
                    ])
                    ->query(
                        fn(Builder $query, array $data): Builder =>
                        $query
                            ->when(
                                $data['sale_month'],
                                fn(Builder $query, $value): Builder => $query->whereMonth('sale_date', $value),
                            )
                            ->when(
                                $data['sale_year'],
                                fn(Builder $query, $value): Builder => $query->whereYear('sale_date', $value),
                            )
                    )
                    ->indicateUsing(function (array $data): array {
                        if ($data['sale_month'] && $data['sale_year']) {
                            $monthName = Carbon::create()->month((int)$data['sale_month'])->translatedFormat('F');
                            $year = $data['sale_year'];

                            return [
                                Indicator::make("Vendas de $monthName de $year")->color('primary')->removable(false),
                            ];
                        }

                        return [];
                    }),

                Filter::make('sale_date')
                    ->label('Data da Venda')
                    ->form([
                        DatePicker::make('sale_from')->label('Venda a partir de'),
                        DatePicker::make('sale_until')->label('Venda até'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['sale_from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('sale_date', '>=', $date),
                            )
                            ->when(
                                $data['sale_until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('sale_date', '<=', $date),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];

                        if ($data['sale_from'] ?? null) {
                            $indicators[] = Indicator::make('Venda a partir de ' . Carbon::parse($data['sale_from'])->translatedFormat("j \\de F"))
                                ->removeField('sale_from');
                        }

                        if ($data['sale_until'] ?? null) {
                            $indicators[] = Indicator::make('Venda até ' . Carbon::parse($data['sale_until'])->translatedFormat("j \\de F"))
                                ->removeField('sale_until');
                        }

                        return $indicators;
                    })
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getFilterYears(): array
    {
        $first = Sale::orderBy('sale_date', 'asc')->first();

        if (!$first) {
            return [date('Y') => date('Y')];
        }

        $years = [];

        $year = Carbon::create($first->sale_date)->year;
        while ($year <= date('Y')) {
            $years[$year] = $year;
            $year++;
        }

        return $years;
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSales::route('/'),
            // 'create' => Pages\CreateSale::route('/create'),
            // 'edit' => Pages\EditSale::route('/{record}/edit'),
        ];
    }
}
